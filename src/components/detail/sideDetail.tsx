//import React from 'react'

import { SideSheet, TabPane, Tabs, Typography } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { ExportConfig } from "./ExportToolbar";
import { FilterColumn } from "./FilterToolbar";
import { RenderSideTabPane } from "./renderSideTabPane";

export type TabPaneProps = {
  entity: string;
  entityTitle: string;
  api: any;
  params?: any;
  scheme: any;
  infoOrList: number;
  child?: any;
  childProps?: any;
  // 过滤功能配置
  enableFilter?: boolean; // 是否启用过滤功能
  filterAtom?: any; // 过滤器状态atom（由父组件传递）
  filterColumns?: FilterColumn[]; // 过滤列配置
  // 导出功能配置
  enableExport?: boolean; // 是否启用导出功能
  exportConfig?: ExportConfig; // 导出配置
  // 分页功能配置
  enablePagination?: boolean; // 是否启用分页功能，默认true
  paginationConfig?: {
    defaultPageSize?: number; // 默认分页大小，默认10
    pageSizeOptions?: number[]; // 分页大小选项，默认[10, 15, 20, 50]
    showSizeChanger?: boolean; // 是否显示分页大小选择器，默认true
    showQuickJumper?: boolean; // 是否显示快速跳转，默认true
  };
};

type SideDetailProps = {
  entityTitle: string;
  detailAtom: any;
  tabPaneList: Array<TabPaneProps>;
};

const SideDetail = (props: SideDetailProps) => {
  const [info, setInfo] = useAtom(props.detailAtom);
  const reset = useResetAtom(props.detailAtom);

  // console.log('info', info)

  return (
    <SideSheet
      title={
        <Typography.Title heading={4}>
          {props.entityTitle}详细信息
        </Typography.Title>
      }
      headerStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      bodyStyle={{
        borderBottom: "1px solid var(--semi-color-border)",
        // added by Trae AI
        // display: "flex",
        // flexDirection: "column",
        // width: "100%",
        // overflow: "hidden",
      }}
      visible={info?.show}
      closeIcon={null} // TODO
      disableScroll={false}
      onCancel={() => {
        reset();
      }}
      size="large"
      // style={{ width: "auto", maxWidth: "80vw" }} // 设置最大宽度为视口宽度的80%
    >
      <Tabs type="card" style={{ width: "100%", flex: 1, overflow: "hidden" }}>
        {props.tabPaneList.map((tabPane, index) => {
          return (
            <TabPane
              tab={tabPane.entityTitle}
              itemKey={tabPane.entity}
              style={{ width: "100%", overflow: "hidden" }}
            >
              {tabPane.child ? (
                // 如果有 child 字段，直接渲染 child 指向的组件
                <tabPane.child {...tabPane.childProps} />
              ) : (
                // 否则使用 RenderSideTabPane 的逻辑
                <RenderSideTabPane
                  entity={tabPane.entity}
                  entityTitle={tabPane.entityTitle}
                  scheme={tabPane.scheme}
                  dataApi={tabPane.api}
                  params={tabPane.params}
                  detailAtom={props.detailAtom}
                  infoOrList={tabPane.infoOrList}
                  enableFilter={tabPane.enableFilter}
                  filterAtom={tabPane.filterAtom}
                  filterColumns={tabPane.filterColumns}
                  enableExport={tabPane.enableExport}
                  exportConfig={tabPane.exportConfig}
                  enablePagination={tabPane.enablePagination}
                  paginationConfig={tabPane.paginationConfig}
                />
              )}
            </TabPane>
          );
        })}
      </Tabs>
    </SideSheet>
  );
};

export default SideDetail;
