# 详情功能使用文档

## 概述

详情功能是一个通用的侧边栏详情展示系统，由 `SideDetail` 和 `RenderSideTabPane` 两个核心组件组成，支持多标签页、多种数据展示方式、过滤和导出功能。

## 核心组件

### 1. SideDetail 组件

**位置**: `src/components/detail/sideDetail.tsx`

**作用**: 侧边栏详情页面的容器组件，负责管理多个标签页和整体布局。

**主要功能**:

- 渲染 Semi UI 的 SideSheet 侧边栏
- 管理多个标签页 (Tabs)
- 控制详情页面的显示/隐藏状态
- 支持自定义组件和标准渲染器

### 2. RenderSideTabPane 组件

**位置**: `src/components/detail/renderSideTabPane.tsx`

**作用**: 标签页内容的渲染引擎，负责根据配置动态渲染不同类型的数据展示。

**主要功能**:

- 基本信息展示 (infoOrList=1)
- 表格列表展示 (infoOrList=2)
- 数据过滤功能
- 导出功能
- 动态字段类型渲染

## 基本用法

### 1. 简单使用示例

```tsx
import SideDetail, { TabPaneProps } from "components/detail/sideDetail";

const MyPage = () => {
  // 定义标签页配置
  const tabPaneList: TabPaneProps[] = [
    {
      entity: "ContractorBasicInfo",
      entityTitle: "基本信息",
      api: getContractor,
      infoOrList: 1, // 基本信息模式
      scheme: [
        {
          groupName: "基础信息",
          list: [
            { name: "name", label: "公司名称" },
            { name: "mobile", label: "联系电话" },
            { name: "address", label: "地址" },
          ],
        },
      ],
    },
    {
      entity: "ContractorEmployeeList",
      entityTitle: "员工信息",
      api: getContractorEmployeeList,
      params: {
        pageNumber: 1,
        pageSize: 50,
        filter: { contractorId: infoAtom?.id },
      },
      infoOrList: 2, // 表格模式
      scheme: [
        {
          table: {
            dataSource: "results",
            columns: [
              { name: "name", label: "姓名" },
              { name: "mobile", label: "手机号码" },
              { name: "idNumber", label: "身份证号" },
            ],
          },
        },
      ],
    },
  ];

  return (
    <div>
      {/* 其他页面内容 */}
      <SideDetail
        entityTitle="承包商"
        detailAtom={contractorDetailSideAtom}
        tabPaneList={tabPaneList}
      />
    </div>
  );
};
```

### 2. 使用自定义组件

```tsx
const tabPaneList: TabPaneProps[] = [
  {
    entity: "CustomComponent",
    entityTitle: "自定义内容",
    child: MyCustomComponent, // 直接指定组件
    childProps: {
      detailAtom: myDetailAtom,
      additionalProp: "value",
    },
  },
];
```

## 配置详解

### TabPaneProps 接口

```tsx
export type TabPaneProps = {
  entity: string; // 实体名称，用于标识
  entityTitle: string; // 标签页标题
  api: any; // API 函数
  params?: any; // API 参数
  scheme: any; // 数据结构配置
  infoOrList: number; // 显示模式：1=基本信息，2=表格列表
  child?: any; // 自定义组件（可选）
  childProps?: any; // 自定义组件属性（可选）

  // 过滤功能配置
  enableFilter?: boolean; // 是否启用过滤功能
  filterAtom?: any; // 过滤器状态 atom
  filterColumns?: FilterColumn[]; // 过滤列配置

  // 导出功能配置
  enableExport?: boolean; // 是否启用导出功能
  exportConfig?: ExportConfig; // 导出配置

  // 分页功能配置
  enablePagination?: boolean; // 是否启用分页功能，默认true
  paginationConfig?: {
    defaultPageSize?: number; // 默认分页大小，默认10
    pageSizeOptions?: number[]; // 分页大小选项，默认[10, 15, 20, 50]
    showSizeChanger?: boolean; // 是否显示分页大小选择器，默认true
    showQuickJumper?: boolean; // 是否显示快速跳转，默认true
  };
};
```

### infoOrList 模式

#### 模式 1: 基本信息展示 (infoOrList=1)

用于展示单个实体的详细信息，以表单样式布局。

**Scheme 结构**:

```tsx
scheme: [
  {
    groupName: "分组名称", // 可选，用于分组显示
    list: [
      {
        name: "字段名",
        label: "显示标签",
        type: "字段类型", // 可选，默认为文本
        // 其他字段类型特定配置...
      },
    ],
  },
];
```

#### 模式 2: 表格列表展示 (infoOrList=2)

用于展示数据列表，以表格形式布局。

**Scheme 结构**:

```tsx
scheme: [
  {
    table: {
      dataSource: "results", // API 返回数据中的数组字段名
      columns: [
        {
          name: "字段名",
          label: "列标题",
          type: "字段类型", // 可选
          width: 120, // 可选，列宽度
          // 其他列配置...
        },
      ],
    },
  },
];
```

## 支持的字段类型

### 基础字段类型

| 类型        | 说明     | 示例                |
| ----------- | -------- | ------------------- |
| `text`      | 纯文本   | 普通字符串内容      |
| `date`      | 日期     | 2024-01-01          |
| `datetime`  | 日期时间 | 2024-01-01 10:30:00 |
| `image`     | 单张图片 | 图片 URL            |
| `imagelist` | 图片列表 | 图片 URL 数组       |
| `file`      | 单个文件 | 文件 URL            |
| `filelist`  | 文件列表 | 文件 URL 数组       |
| `video`     | 视频     | 视频 URL            |

### 关联字段类型

| 类型     | 说明     | 配置                             |
| -------- | -------- | -------------------------------- |
| `entity` | 关联实体 | `key: "name"` 指定显示字段       |
| `dic`    | 字典值   | 显示 `dicValue` 字段             |
| `enum`   | 枚举值   | `enumMap: ENUM_MAP` 指定枚举映射 |
| `array`  | 数组     | 显示为标签列表                   |

### 特殊字段类型

| 类型     | 说明     | 配置                          |
| -------- | -------- | ----------------------------- |
| `button` | 操作按钮 | `buttons: [...]` 按钮配置数组 |

### 特殊渲染模式

除了基本的字段类型外，系统还支持特殊的渲染模式：

| render 值 | 说明         | 适用类型    | 示例                           |
| --------- | ------------ | ----------- | ------------------------------ |
| `expire`  | 过期时间渲染 | `date`      | 根据时间距离显示不同颜色的标签 |
| `rate`    | 百分比渲染   | 数值        | 格式化为百分比显示             |
| `link`    | 链接渲染     | 任意        | 可点击跳转到其他页面           |
| `old`     | 旧格式图片   | `imagelist` | 解析 JSON 字符串为图片数组     |
| `entity`  | 实体数组渲染 | `array`     | 显示数组中每个实体的名称       |

### 特殊配置属性

| 属性      | 说明         | 使用场景                      |
| --------- | ------------ | ----------------------------- |
| `padding` | 添加空白行   | 基本信息展示中用于分隔        |
| `key`     | 实体显示字段 | `entity` 类型指定显示哪个字段 |
| `width`   | 列宽度       | 表格列配置                    |
| `color`   | 枚举颜色     | `enum` 类型的彩色标签         |
| `gridCol` | 网格列数     | 控制基本信息的列布局，默认 6  |

### 字段类型配置示例

```tsx
// 基础字段
{ name: "name", label: "名称" }, // 默认文本
{ name: "description", label: "描述", type: "text" },

// 日期时间
{ name: "createTime", label: "创建时间", type: "datetime" },
{ name: "expireDate", label: "过期日期", type: "date" },

// 文件和图片
{ name: "avatar", label: "头像", type: "image" },
{ name: "attachments", label: "附件", type: "filelist" },

// 关联实体
{
  name: "department",
  label: "部门",
  type: "entity",
  key: "name" // 显示 department.name
},

// 枚举值
{
  name: "status",
  label: "状态",
  type: "enum",
  enumMap: STATUS_MAP
},

// 操作按钮
{
  name: "actions",
  label: "操作",
  type: "button",
  buttons: [
    (record) => ({
      chnName: "编辑",
      func: (record) => editRecord(record),
    }),
  ],
},

// 特殊渲染模式示例
{
  name: "expireDate",
  label: "到期日期",
  type: "date",
  render: "expire", // 根据时间距离显示不同颜色
},

{
  name: "completion",
  label: "完成率",
  render: "rate", // 格式化为百分比
},

{
  name: "judgeResult",
  label: "检查结果",
  type: "enum",
  enumMap: JUDGERESULT_MAP,
  render: "link", // 可点击链接
  link: "/danger", // 跳转路径
  filterField: "dangerId", // 过滤字段
  filterTargetField: "id", // 目标字段
},

{
  name: "images",
  label: "证书图片",
  type: "imagelist",
  render: "old", // 解析旧格式 JSON 字符串
},

{
  name: "candidatePersons",
  label: "候选人",
  type: "array",
  render: "entity", // 显示实体数组的名称
},

// 带颜色的枚举标签
{
  name: "status",
  label: "状态",
  type: "enum",
  enumMap: [
    { id: 1, name: "正常", color: "green" },
    { id: 2, name: "警告", color: "yellow" },
    { id: 3, name: "危险", color: "red" },
  ],
},

// 添加空白行分隔
{
  padding: true, // 在基本信息中添加空白行
},

// 自定义网格列数
// 在 RenderSideTabPane 中: gridCol={4} // 改为4列布局
```

## 过滤功能

### 启用过滤功能

1. **创建过滤器状态 Atom**:

```tsx
// 在 atoms 目录中创建
export const myFilterAtom = atom({
  filter: {},
  query: "",
});
```

2. **配置过滤列**:

```tsx
const tabPaneList: TabPaneProps[] = [
  {
    // ... 其他配置
    enableFilter: true,
    filterAtom: myFilterAtom,
    filterColumns: [
      {
        field: "department",
        label: "部门",
        component: "DepartmentSearch",
        props: {
          placeholder: "请选择部门",
        },
      },
      {
        field: "status",
        label: "状态",
        component: "Select",
        props: {
          placeholder: "请选择状态",
          optionList: STATUS_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
    ],
  },
];
```

### 支持的过滤组件

| 组件类型           | 说明       | 配置示例                        |
| ------------------ | ---------- | ------------------------------- |
| `DepartmentSearch` | 部门选择器 | `{ placeholder: "请选择部门" }` |
| `RestSelect`       | 下拉选择器 | `{ optionList: [...] }`         |
| `Input`            | 文本输入框 | `{ placeholder: "请输入..." }`  |
| `DatePicker`       | 日期选择器 | `{ type: "date" }`              |

### 过滤器类型配置

```tsx
// 下拉选择
{
  field: "status",
  label: "状态",
  component: "Select",
  props: {
    optionList: [
      { value: 1, label: "启用" },
      { value: 0, label: "禁用" },
    ],
  },
},

// 日期范围
{
  field: "dateRange",
  label: "日期范围",
  component: "DatePicker",
  props: {
    type: "dateRange",
  },
  // API 字段映射
  apiFieldMap: {
    start: "startDate",
    end: "endDate",
  },
},
```

## 导出功能

### 启用导出功能

1. **在根组件添加 ExportProvider**:

```tsx
import { ExportProvider } from "components/export/exportProvider";

const MyPage = () => {
  return (
    <ExportProvider>
      <div>
        {/* 页面内容 */}
        <SideDetail ... />
      </div>
    </ExportProvider>
  );
};
```

2. **配置导出选项**:

```tsx
const tabPaneList: TabPaneProps[] = [
  {
    // ... 其他配置
    enableExport: true,
    exportConfig: {
      filename: "数据导出",
      formats: ["excel", "csv"], // 支持的格式
      columns: [
        "name",
        "mobile",
        "status",
        // ... 要导出的列字段名
      ],
    },
  },
];
```

### 导出配置

```tsx
interface ExportConfig {
  filename: string; // 导出文件名
  formats: ("excel" | "csv")[]; // 支持的导出格式
  columns?: string[]; // 要导出的列，不指定则导出所有列
}
```

## 分页功能

### 启用分页功能

分页功能默认启用，可以显著提升大数据量场景下的性能和用户体验。

#### 1. 基本配置

```tsx
const tabPaneList: TabPaneProps[] = [
  {
    // ... 其他配置
    enablePagination: true, // 启用分页功能（默认值）
    paginationConfig: {
      defaultPageSize: 10, // 默认每页显示10条
      pageSizeOptions: [10, 20, 50, 100], // 分页大小选项
      showSizeChanger: true, // 显示分页大小选择器
      showQuickJumper: true, // 显示快速跳转
    },
  },
];
```

#### 2. 禁用分页功能

如果需要加载所有数据（不推荐用于大数据量），可以禁用分页：

```tsx
{
  // ... 其他配置
  enablePagination: false, // 禁用分页，使用原有的大数据量加载
}
```

#### 3. 自定义分页配置

```tsx
{
  // ... 其他配置
  enablePagination: true,
  paginationConfig: {
    defaultPageSize: 20, // 自定义默认分页大小
    pageSizeOptions: [20, 50, 100], // 自定义分页选项
    showSizeChanger: false, // 隐藏分页大小选择器
    showQuickJumper: false, // 隐藏快速跳转
  },
}
```

### 分页配置选项

```tsx
interface PaginationConfig {
  defaultPageSize?: number; // 默认分页大小，默认10
  pageSizeOptions?: number[]; // 分页大小选项，默认[10, 15, 20, 50]
  showSizeChanger?: boolean; // 是否显示分页大小选择器，默认true
  showQuickJumper?: boolean; // 是否显示快速跳转，默认true
}
```

### 分页与过滤功能集成

分页功能与过滤功能完全兼容，当应用过滤条件时，页码会自动重置到第一页：

```tsx
{
  // ... 其他配置
  enableFilter: true,
  filterAtom: myFilterAtom,
  filterColumns: [...],
  enablePagination: true,
  paginationConfig: {
    defaultPageSize: 10,
    pageSizeOptions: [10, 20, 50],
  },
}
```

### 分页与导出功能集成

分页功能与导出功能完全兼容，导出时会导出所有符合过滤条件的数据，而不仅仅是当前页的数据：

```tsx
{
  // ... 其他配置
  enablePagination: true,
  enableExport: true,
  exportConfig: {
    filename: "数据导出",
    formats: ["excel"],
  },
}
```

### 性能优化

启用分页功能后的性能提升：

| 指标           | 禁用分页   | 启用分页 | 提升        |
| -------------- | ---------- | -------- | ----------- |
| 初始加载数据量 | 99999条    | 10条     | 99.99% ⬇️   |
| 网络传输量     | 全量数据   | 分页数据 | 90%+ ⬇️     |
| 内存占用       | 全量缓存   | 分页缓存 | 90%+ ⬇️     |
| 用户体验       | 长时间等待 | 快速响应 | 显著提升 ⬆️ |

## 实际应用示例

### 1. 承包商详情页 (contractorPage.tsx)

```tsx
export function ContractorPage() {
  const tabPaneList: TabPaneProps[] = [
    // 基本信息
    {
      entity: "ContractorBasicInfo",
      entityTitle: "基本信息",
      api: getContractor,
      infoOrList: 1,
      scheme: [
        {
          groupName: "",
          list: [
            { name: "name", label: "公司名称" },
            { name: "creditCode", label: "统一社会信用代码" },
            { name: "representative", label: "法定代表" },
            { name: "mobile", label: "法人电话" },
            { name: "address", label: "公司地址" },
            { name: "serviceBeginDate", label: "服务开始时间", type: "date" },
            { name: "serviceEndDate", label: "服务结束时间", type: "date" },
            { name: "information", label: "公司介绍", type: "text" },
            { name: "licenseList", label: "营业执照", type: "image" },
          ],
        },
      ],
    },
    // 员工信息表格
    {
      entity: "ContractorEmployeeList",
      entityTitle: "员工信息",
      api: getContractorEmployeeList,
      params: {
        pageNumber: 1,
        pageSize: 50,
        filter: { contractorId: infoAtom?.id },
      },
      infoOrList: 2,
      scheme: [
        {
          table: {
            dataSource: "results",
            columns: [
              { name: "name", label: "姓名" },
              { name: "mobile", label: "手机号码" },
              { name: "idNumber", label: "身份证号" },
              {
                name: "status",
                label: "人员状态",
                type: "enum",
                enumMap: CONTRACTOR_STATUS_MAP,
              },
              {
                name: "idCardImageList",
                label: "身份证照片",
                type: "imagelist",
              },
            ],
          },
        },
      ],
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <ContractorFilter />
      <ContractorModal />
      <SideDetail
        entityTitle="承包商"
        detailAtom={contractorDetailSideAtom}
        tabPaneList={tabPaneList}
      />
      <ContractorContent />
    </div>
  );
}
```

### 2. 培训计划详情页 (planPage.tsx) - 带过滤和导出

```tsx
export function CoporateTrainingPlanPage() {
  const tabPaneList: TabPaneProps[] = [
    // 基本信息
    {
      entity: "CoporateTrainingPlanBasicInfo",
      entityTitle: "基本信息",
      api: getCoporateTrainingPlan,
      infoOrList: 1,
      scheme: [
        {
          groupName: "",
          list: [
            { label: "培训计划名称", name: "name" },
            { label: "培训类型", name: "type", type: "dic" },
            { label: "开始时间", name: "beginTime", type: "datetime" },
            { label: "结束时间", name: "endTime", type: "datetime" },
            { label: "培训地点", name: "place" },
            { label: "培训内容", name: "content", type: "text" },
          ],
        },
      ],
    },
    // 培训人员 - 带过滤、导出和分页功能
    {
      entity: "CoporateTrainingPlanPeople",
      entityTitle: "培训人员情况",
      api: getCoporateTrainingPlanPeople,
      infoOrList: 2,
      params: {
        id: infoAtom?.id,
        values: {}, // 移除固定分页参数，由分页功能动态管理
      },
      // 过滤功能
      enableFilter: true,
      filterAtom: coporateTrainingPlanPeopleFilterAtom,
      filterColumns: [
        {
          field: "personUnit",
          label: "部门/单位",
          component: "DepartmentSearch",
          props: { placeholder: "请选择部门/单位" },
        },
        {
          field: "status",
          label: "状态",
          component: "Select",
          props: {
            placeholder: "请选择状态",
            optionList: TRAINING_RECORD_STATUS_MAP.map((item) => ({
              value: item.id,
              label: item.name,
            })),
          },
        },
      ],
      // 导出功能
      enableExport: true,
      exportConfig: {
        filename: "培训人员情况",
        formats: ["excel"],
        columns: [
          "person",
          "employeeId",
          "idNumber",
          "personUnit",
          "trainingBeginTime",
          "trainingEndTime",
          "status",
        ],
      },
      // 分页功能
      enablePagination: true,
      paginationConfig: {
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        showQuickJumper: true,
      },
      scheme: [
        {
          table: {
            dataSource: "results",
            columns: [
              {
                label: "学员姓名",
                name: "person",
                type: "entity",
                key: "name",
              },
              { label: "工号", name: "employeeId" },
              { label: "身份证号", name: "idNumber" },
              {
                label: "部门/单位",
                name: "personUnit",
                type: "entity",
                key: "name",
              },
              {
                label: "开始时间",
                name: "trainingBeginTime",
                type: "datetime",
              },
              { label: "结束时间", name: "trainingEndTime", type: "datetime" },
              {
                label: "状态",
                name: "status",
                type: "enum",
                enumMap: TRAINING_RECORD_STATUS_MAP,
              },
            ],
          },
        },
      ],
    },
  ];

  return (
    <ExportProvider>
      <div className="flex flex-col gap-4">
        <CoporateTrainingPlanFilter />
        <CoporateTrainingPlanModal />
        <SideDetail
          entityTitle="培训计划"
          detailAtom={coporateTrainingPlanDetailSideAtom}
          tabPaneList={tabPaneList}
        />
        <CoporateTrainingPlanContent />
      </div>
    </ExportProvider>
  );
}
```

### 3. 生产装置详情页 (productionUnitPage.tsx) - 带过滤和分页功能

```tsx
export function ProductionUnitPage() {
  const [detailSideAtom, setDetailSideAtom] = useAtom(
    productionUnitDetailSideAtom
  );

  const maintenanceTab = {
    entity: "ProductionUnitMaintenanceList",
    entityTitle: "维保信息",
    api: getProductionUnitMaintenanceList,
    params: {
      id: detailSideAtom?.id,
      values: {}, // 移除固定分页参数，由分页功能动态管理
    },
    infoOrList: 2,
    enableFilter: true,
    filterAtom: productionUnitMaintenanceFilterAtom,
    filterColumns: [
      {
        field: "reportStatus",
        label: "上报状态",
        component: "Select",
        props: {
          placeholder: "请选择上报状态",
          optionList: JOB_REPORT_STATUS_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
    ],
    // 分页功能配置
    enablePagination: true,
    paginationConfig: {
      defaultPageSize: 10,
      pageSizeOptions: [10, 20, 50],
      showSizeChanger: true,
      showQuickJumper: true,
    },
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "检修名称", name: "name" },
            { label: "检修内容", name: "content" },
            { label: "开始时间", name: "startTime", type: "date" },
            { label: "计划结束时间", name: "planEndTime", type: "date" },
            { label: "实际结束时间", name: "endTime", type: "date" },
            { label: "备注", name: "note" },
            { label: "上报时间", name: "reportTime", type: "date" },
            {
              label: "上报状态",
              name: "reportStatus",
              type: "enum",
              enumMap: JOB_REPORT_STATUS_MAP,
            },
            { label: "上报结果", name: "reportResult" },
          ],
        },
      },
    ],
  };

  const tabPaneList = [basicTab, maintenanceTab, startStopTab];

  return (
    <div className="flex flex-col gap-4">
      <ProductionUnitFilter />
      <ProductionUnitModal />
      <SideDetail
        entityTitle="生产装置详情"
        detailAtom={productionUnitDetailSideAtom}
        tabPaneList={tabPaneList}
      />
      <ProductionUnitContent />
    </div>
  );
}
```

### 4. 使用自定义组件 (inspectionTaskRecordPage.tsx)

```tsx
export function InspectionTaskRecordPage() {
  const tabPaneList: TabPaneProps[] = [
    // 标准配置的基本信息
    {
      entity: "InspectionTaskRecordBasicInfo",
      entityTitle: "基本信息",
      api: getInspectionTask,
      infoOrList: 1,
      scheme: [
        {
          groupName: "",
          list: [
            {
              label: "巡检计划",
              name: "inspectionPlan",
              type: "entity",
              key: "name",
            },
            {
              label: "巡检类型",
              name: "inspectionType",
              type: "enum",
              enumMap: INTELLIGENTINSPECTION_TYPE_MAP,
            },
            {
              label: "责任部门",
              name: "liableDepartment",
              type: "entity",
              key: "name",
            },
            { label: "任务开始时间", name: "taskBeginTime", type: "datetime" },
            { label: "任务结束时间", name: "taskEndTime", type: "datetime" },
          ],
        },
      ],
    },
    // 自定义组件
    {
      entity: "InspectionTaskRecordProgress",
      entityTitle: "巡检进展",
      child: IntelligentInspectionTaskProgressPage,
      childProps: {
        detailAtom: inspectionTaskRecordDetailSideAtom,
      },
    },
    // 另一个自定义组件
    {
      entity: "InspectionTaskRecordPlaceList",
      entityTitle: "巡检点",
      child: PlaceDetail,
      childProps: {
        detailAtom: inspectionTaskRecordDetailSideAtom,
      },
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <InspectionTaskRecordFilter />
      <InspectionTaskRecordModal />
      <InspectionTaskRecordContent />
      <SideDetail
        entityTitle="巡检任务记录"
        detailAtom={inspectionTaskRecordDetailSideAtom}
        tabPaneList={tabPaneList}
      />
    </div>
  );
}
```

## 最佳实践

### 1. 数据状态管理

- 每个详情页使用独立的 detailAtom 来管理显示状态
- 过滤功能使用独立的 filterAtom 来管理过滤状态
- Atom 命名规范：`{module}{Entity}DetailSideAtom`、`{module}{Entity}FilterAtom`

### 2. API 参数结构

系统支持两种 API 参数结构：

```tsx
// 结构1: 带 values 包装
{
  id: "123",
  values: {
    pageNumber: 1,
    pageSize: 50,
    filter: { ... }
  }
}

// 结构2: 扁平结构
{
  id: "123",
  pageNumber: 1,
  pageSize: 50,
  filter: { ... }
}
```

### 3. 字段配置建议

- 优先使用语义化的字段类型（如 `date`、`datetime`、`entity`）
- 合理使用 `groupName` 对基本信息进行分组
- 表格列配置建议指定 `width` 避免布局问题
- 枚举字段务必配置 `enumMap` 确保正确显示

### 4. 性能优化

- 大数据量表格建议配置分页参数
- 图片和文件字段使用 `imagelist`/`filelist` 类型
- 合理使用过滤功能减少数据量

### 5. 错误处理

- API 调用失败时会显示空状态
- 字段值为空时会显示 "-"
- 图片加载失败会有默认占位符

## 注意事项

1. **ExportProvider 依赖**: 使用导出功能时必须在上层组件添加 `ExportProvider`
2. **Atom 管理**: 确保每个功能模块使用独立的 atom，避免状态冲突
3. **API 契约**: API 返回数据结构需要与 `scheme.table.dataSource` 配置匹配
4. **类型安全**: 建议为 `TabPaneProps` 配置 TypeScript 类型检查
5. **样式覆盖**: 如需自定义样式，可通过 CSS 类名覆盖默认样式

## 高级用法

### 1. 静态数据组件 (RenderSideTabPaneWithStaticData)

当数据已经在前端准备好，不需要 API 调用时，可以使用静态数据版本：

**位置**: `src/components/detail/renderSideTabPaneWithStaticData.tsx`

```tsx
import { RenderSideTabPaneWithStaticData } from "components/detail/renderSideTabPaneWithStaticData";

// 在 equipmentPage.tsx 的 expandedDataCallback 中使用
const expandRowRender = (record: any, index: number) => {
  return (
    <div style={{ width: "100%", padding: 0 }}>
      {/* 基本信息部分 */}
      <RenderSideTabPaneWithStaticData
        entity="IntelligentInspectionTaskRecordPlace"
        entityTitle={expandedData[index]?.name ?? "暂无数据"}
        scheme={basicScheme}
        infoOrList={1}
        dataSource={expandedData[index]} // 直接传入数据
        style={{ width: "100%", overflow: "hidden" }}
      />

      {/* 表格部分 */}
      <RenderSideTabPaneWithStaticData
        entity="IntelligentInspectionTaskRecordStandard"
        entityTitle="巡检记录"
        scheme={standardScheme}
        infoOrList={2}
        dataSource={expandedData[index]?.standards} // 传入表格数据
      />
    </div>
  );
};
```

**与普通组件的区别**:

- 不需要 `api` 和 `params` 参数
- 直接通过 `dataSource` 传入数据
- 支持 `style` 参数进行样式自定义

### 2. 可展开表格和嵌套展示

在设备管理页面中展示了如何使用 `expandedDataCallback` 创建可展开的表格：

```tsx
// equipmentPage.tsx 示例
{
  entity: "EquipmentManagementEquipmentIiPlaceList",
  entityTitle: "点巡检",
  child: RenderSideDetailList,
  childProps: {
    entity: "EquipmentManagementEquipmentIiPlaceDetail",
    columnsAtom: equipmentInspectionColumnsAtom,
    filterAtom: equipmentInspectionFilterAtom,
    queryApi: getEquipmentInspectionList,
    detailAtom: equipmentManagementEquipmentDetailSideAtom,
    expandedDataCallback: expandedDataCallback, // 展开行渲染函数
  },
}

// expandedDataCallback 的实现
const expandedDataCallback = (expandedData: any) => {
  const expandRowRender = (record: any, index: number) => {
    return (
      <div style={{
        width: `calc(100vw - ${sidebarWidth}px)`,
        overflow: "hidden",
      }}>
        {/* 嵌套的详情展示 */}
        <RenderSideTabPaneWithStaticData
          entity="NestedDetails"
          entityTitle={expandedData[index]?.name}
          scheme={nestedScheme}
          infoOrList={1}
          dataSource={expandedData[index]}
        />
      </div>
    );
  };
  return expandRowRender;
};
```

### 3. 自定义组件集成

使用 `child` 和 `childProps` 完全自定义标签页内容：

```tsx
{
  entity: "CustomMiscDetail",
  entityTitle: "其他信息",
  child: MiscDetail, // 自定义组件
  childProps: {
    detailAtom: equipmentManagementEquipmentDetailSideAtom,
    additionalConfig: "value",
  },
}
```

### 4. 链接跳转配置

表格中的链接点击跳转功能：

```tsx
{
  name: "judgeResult",
  label: "检查结果",
  type: "enum",
  enumMap: JUDGERESULT_MAP,
  render: "link",
  link: "/double_guard/danger/danger", // 跳转路径
  filterField: "dangerId", // 传递的过滤字段
  filterTargetField: "id", // 目标页面的过滤字段
  width: 100,
}
```

点击该列时会：

1. 根据 `setFilterList[col.name]` 设置目标页面的过滤条件
2. 使用 `navigate(col.link)` 跳转到指定页面
3. 传递 `record[col.filterField]` 的值给目标页面的 `col.filterTargetField` 字段

## 组件变体总览

| 组件                              | 用途       | 数据来源 | 主要特性                     |
| --------------------------------- | ---------- | -------- | ---------------------------- |
| `SideDetail`                      | 主容器     | -        | 侧边栏布局、多标签页管理     |
| `RenderSideTabPane`               | 标准渲染器 | API 调用 | 支持过滤、导出、动态数据加载 |
| `RenderSideTabPaneWithStaticData` | 静态渲染器 | 静态数据 | 不依赖 API，适合嵌套展示     |
| `RenderSideDetailList`            | 列表渲染器 | API 调用 | 专门用于列表展示，支持展开行 |

## 相关文件

- `src/components/detail/sideDetail.tsx` - 主容器组件
- `src/components/detail/renderSideTabPane.tsx` - 标准渲染引擎
- `src/components/detail/renderSideTabPaneWithStaticData.tsx` - 静态数据渲染引擎
- `src/components/detail/renderSideTabPaneList.tsx` - 列表渲染引擎
- `src/components/detail/FilterToolbar.tsx` - 过滤工具栏
- `src/components/detail/ExportToolbar.tsx` - 导出工具栏
- `src/components/export/exportProvider.tsx` - 导出功能提供者

通过这套详情功能系统，可以快速构建功能丰富的数据详情页面，支持多种展示方式和交互功能。
