import {
  getIcon,
  pickProvinceColor,
  shadeColor,
} from "components/region_plugins/utils";
import type React from "react";

import {
  HebeiConfigContent,
  JiangxiConfigContent,
  JiangxiLepingConfigContent,
  JiangxiPlatformConfigContent,
  JiangxiWannianFengchaoConfigContent,
  LiaoningPanjinConfigContent,
  YunNanConfigContent,
  ZhejiangConfigContent,
} from "./regionPluginConfigForms";

/** 展示 & UI 需要的插件信息 */
export interface RegionPluginInfo {
  readonly name: string;
  readonly description: string;
  readonly icon: React.ReactNode;
  readonly color: string;
  readonly version: string;
  readonly lastUpdate: string;
  readonly enabled?: boolean;
}

/** 元数据项：id + info (+ Form) */
export interface RegionPluginMeta {
  readonly id: number;
  readonly info: RegionPluginInfo;
  /** 具体地区表单组件，必须存在 */
  readonly Form: React.FC;
}

/**
 * 上报插件清单 —— 单一数据源
 * 后续增删插件只需改此处。
 */
export const REGION_PLUGIN_META: ReadonlyArray<RegionPluginMeta> = [
  {
    id: 8,
    info: {
      name: "江西省平台",
      description: "江西省平台特殊作业上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("J"),
      version: "1.0.0",
      lastUpdate: "2025-09-08",
    },
    Form: JiangxiPlatformConfigContent,
  },
  {
    id: 7,
    info: {
      name: "浙江省平台",
      description: "浙江省平台特殊作业上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("Z"),
      version: "1.0.0",
      lastUpdate: "2025-09-01",
    },
    Form: ZhejiangConfigContent,
  },

  {
    id: 1,
    info: {
      name: "云南省平台",
      description: "云南省平台特殊作业上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("Y"),
      version: "v1.2.0",
      lastUpdate: "2025-07-11",
      enabled: true,
    },
    Form: YunNanConfigContent,
  },
  {
    id: 2,
    info: {
      name: "江西-新干盐化城",
      description: "配置江西新干盐化城特有的上报参数",
      icon: getIcon("zone"),
      color: shadeColor(pickProvinceColor("J"), 0),
      version: "v1.0.5",
      lastUpdate: "2025-07-11",
      enabled: true,
    },
    Form: JiangxiConfigContent,
  },
  {
    id: 3,
    info: {
      name: "江西-万年凤巢工业区",
      description: "配置江西万年凤巢工业区特有的上报参数",
      icon: getIcon("zone"),
      color: shadeColor(pickProvinceColor("J"), 4),
      version: "v1.0.0",
      lastUpdate: "2025-07-11",
      enabled: true,
    },
    Form: JiangxiWannianFengchaoConfigContent,
  },
  {
    id: 4,
    info: {
      name: "辽宁-盘锦",
      description: "配置辽宁盘锦特有的上报参数",
      icon: getIcon("city"),
      color: pickProvinceColor("L"),
      version: "v1.0.0",
      lastUpdate: "2025-07-11",
      enabled: true,
    },
    Form: LiaoningPanjinConfigContent,
  },
  {
    id: 5,
    info: {
      name: "河北省平台",
      description: "河北省平台特殊作业上报配置",
      icon: getIcon("province"),
      color: pickProvinceColor("H"),
      version: "v1.0.0",
      lastUpdate: "2025-07-21",
      enabled: true,
    },
    Form: HebeiConfigContent,
  },
  {
    id: 6,
    info: {
      name: "江西-乐平工业园",
      description: "配置江西乐平工业园特有的上报参数",
      icon: getIcon("zone"),
      color: shadeColor(pickProvinceColor("J"), 8),
      version: "v1.0.0",
      lastUpdate: "2025-08-01",
      enabled: true,
    },
    Form: JiangxiLepingConfigContent,
  },
] as const;
