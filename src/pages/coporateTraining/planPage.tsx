/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import { Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  BackCoporateTrainingPlan,
  LeaveCoporateTrainingPlan,
  getCoporateTrainingPlan,
  getCoporateTrainingPlanDepartment,
  getCoporateTrainingPlanPeople,
  getCoporateTrainingPlanPosition,
} from "api";
import {
  coporateTrainingPlanDetailSideAtom,
  coporateTrainingPlanPeopleFilterAtom,
} from "atoms";
import {
  IS_ISNOT_MAP,
  TRAINING_PLAN_OBJECTTYPE_MAP,
  TRAINING_RECORD_STATUS_MAP,
} from "components";
import SideDetail, { TabPaneProps } from "components/detail/sideDetail";
import { ExportProvider } from "components/export";
import { UnitType } from "components/search";
import { use<PERSON>tom } from "jotai";
import { useCallback } from "react";
import {
  CoporateTrainingPlanContent,
  CoporateTrainingPlanFilter,
} from "./content";
import { ContractorTrainingPlanShareModal } from "./modal/contractorTrainingPlanShareModal";
import { CoporateTrainingPlanModal } from "./modal/planModal";

export function CoporateTrainingPlanPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  const queryClient = useQueryClient();

  const [infoAtom, setInfoAtom] = useAtom(coporateTrainingPlanDetailSideAtom);

  const LeaveMutation = useMutation({
    mutationFn: LeaveCoporateTrainingPlan,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({
          queryKey: [`get${tabPaneList[1].entity}`, infoAtom?.id],
        });
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleLeave = useCallback(
    (record) => {
      LeaveMutation.mutate({
        id: infoAtom?.id,
        recordId: record.id,
      });
    },
    [LeaveMutation]
  );

  const BackMutation = useMutation({
    mutationFn: BackCoporateTrainingPlan,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({
          queryKey: [`get${tabPaneList[1].entity}`, infoAtom?.id],
        });
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleBack = useCallback(
    (record) => {
      BackMutation.mutate({
        id: infoAtom?.id,
        recordId: record.id,
      });
    },
    [BackMutation]
  );

  const toggleLeave = (record) => {
    // 1. 未完成; 2. 通过; 3. 未通过; 4. 请假
    if (record?.status === 1) {
      return {
        engName: "leave",
        chnName: "请假",
        func: handleLeave,
      };
    } else if (record?.status === 4) {
      return {
        engName: "back",
        chnName: "返岗",
        func: handleBack,
      };
    }
  };

  const tabPaneList: TabPaneProps[] = [
    {
      entity: "CoporateTrainingPlan",
      entityTitle: "基本信息",
      api: getCoporateTrainingPlan,
      infoOrList: 1,
      scheme: [
        {
          groupName: "",
          list: [
            { label: "培训计划名称", name: "name" },
            { label: "培训类型", name: "trainingTypeValue", type: "dic" },
            { label: "开始时间", name: "beginTime", type: "datetime" },
            { label: "结束时间", name: "endTime", type: "datetime" },
            {
              label: "培训对象",
              name: "objectType",
              type: "enum",
              enumMap: TRAINING_PLAN_OBJECTTYPE_MAP,
            },
            {
              label: "通过是否获得入场资格",
              name: "isEntryPass",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
            { label: "入厂资格有效期", name: "entryValidMonths" },
            {
              label: "通过获得培训证书",
              name: "passCertificate",
              type: "entity",
              key: "certificateTypeValueName",
            },
            { label: "课程列表", name: "courses", type: "array" },
            { label: "培训讲师", name: "teachers", type: "array" },
            { label: "培训地点", name: "place" },
            { label: "培训内容", name: "content", type: "text", padding: true },
            { label: "培训计划封面", name: "image", type: "image" },
          ],
        },
      ],
    },
    {
      entity: "CoporateTrainingPlanPeople",
      entityTitle: "培训人员情况",
      api: getCoporateTrainingPlanPeople,
      infoOrList: 2,
      params: {
        id: infoAtom?.id,
        values: {}, // 移除固定的分页参数，由分页功能动态管理
      },
      // 过滤功能配置
      enableFilter: true,
      filterAtom: coporateTrainingPlanPeopleFilterAtom,
      filterColumns: [
        {
          field: "personUnit",
          label: "部门/单位",
          component: "DepartmentUnitSearch",
          props: {
            placeholder: "请选择部门/单位",
            unitType:
              infoAtom?.plan?.objectType === 3
                ? UnitType.Contractor
                : UnitType.Department,
            fieldKeyMap: {
              [UnitType.Department]: "departmentId",
              [UnitType.Contractor]: "contractorId",
            },
          },
        },
        {
          field: "status",
          label: "状态",
          component: "Select",
          props: {
            placeholder: "请选择状态",
            optionList: TRAINING_RECORD_STATUS_MAP.map((item) => ({
              value: item.id,
              label: item.name,
            })),
          },
        },
      ],

      // 导出功能配置
      enableExport: true,
      exportConfig: {
        filename: "培训人员情况",
        formats: ["excel"],
        columns: [
          "person",
          "employeeId",
          "idNumber",
          "personUnit",
          "trainingBeginTime",
          "trainingEndTime",
          "status",
        ], // 指定要导出的列
      },

      // 分页功能配置
      enablePagination: true,
      paginationConfig: {
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        showQuickJumper: true,
      },
      scheme: [
        {
          table: {
            dataSource: "results",
            columns: [
              {
                label: "学员姓名",
                name: "person",
                type: "entity",
                key: "name",
              },
              { label: "工号", name: "employeeId" },
              { label: "身份证号", name: "idNumber" },
              {
                label: "部门/单位",
                name: "personUnit",
                type: "entity",
                key: "name",
              },
              {
                label: "开始时间",
                name: "trainingBeginTime",
                type: "datetime",
              },
              { label: "结束时间", name: "trainingEndTime", type: "datetime" },
              {
                label: "状态",
                name: "status",
                type: "enum",
                enumMap: TRAINING_RECORD_STATUS_MAP,
              },
              {
                label: "操作",
                name: "-",
                type: "button",
                buttons: [toggleLeave],
              },
            ],
          },
        },
      ],
    },
    {
      entity: "CoporateTrainingPlanDepartment",
      entityTitle: "部门统计",
      api: getCoporateTrainingPlanDepartment,
      params: { id: infoAtom?.id },
      infoOrList: 2,
      scheme: [
        {
          table: {
            dataSource: "",
            columns: [
              {
                label: "部门/单位",
                name: "department",
                type: "entity",
                key: "name",
              },
              { label: "参与人数", name: "candidateNum" },
              { label: "通过人数", name: "passNum" },
              { label: "未通过人数", name: "unpassNum" },
              { label: "未完成人数", name: "ongoingNum" },
              // { label: "请假人数", name: "leaveNum" },
              { label: "通过率", name: "passRate", render: "rate" },
            ],
          },
        },
      ],
    },
    {
      entity: "CoporateTrainingPlanPosition",
      entityTitle: "岗位统计",
      api: getCoporateTrainingPlanPosition,
      params: { id: infoAtom?.id },
      infoOrList: 2,
      scheme: [
        {
          table: {
            dataSource: "",
            columns: [
              { label: "岗位", name: "position", type: "entity", key: "name" },
              { label: "参与人数", name: "candidateNum" },
              { label: "通过人数", name: "passNum" },
              { label: "未通过人数", name: "unpassNum" },
              { label: "未完成人数", name: "ongoingNum" },
              // { label: '请假人数', name: 'leaveNum' },
              { label: "通过率", name: "passRate", render: "rate" },
            ],
          },
        },
      ],
    },
  ];

  return (
    <ExportProvider>
      <div className="flex flex-col gap-4 ">
        {/* <RoleSide />
          <RoleModal />
        <RoleFilter />
        <RoleContent /> */}
        <CoporateTrainingPlanFilter filter={filter} />
        <CoporateTrainingPlanModal />
        <ContractorTrainingPlanShareModal />
        <SideDetail
          entityTitle="培训计划"
          detailAtom={coporateTrainingPlanDetailSideAtom}
          tabPaneList={tabPaneList}
        />
        <CoporateTrainingPlanContent
          readonly={readonly}
          filter={filter}
          {...restProps}
        />
      </div>
    </ExportProvider>
  );
}
