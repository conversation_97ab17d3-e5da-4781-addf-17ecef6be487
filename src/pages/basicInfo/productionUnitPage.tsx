/* import { <PERSON>F<PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  getProductionUnit,
  getProductionUnitMaintenanceList,
  getProductionUnitStartStopList,
} from "api";
import {
  productionUnitDetailSideAtom,
  productionUnitMaintenanceFilterAtom,
  productionUnitStartStopFilterAtom,
} from "atoms";
import {
  IS_ISNOT_MAP,
  JOB_REPORT_STATUS_MAP,
  PRODUCTIONUNIT_STARSTOP_STATUS_MAP,
  PRODUCTIONUNIT_STARTSTOP_TYPE_MAP,
  PRODUCTIONUNIT_STATUS_MAP,
} from "components";
import SideDetail from "components/detail/sideDetail";
import { useAtom } from "jotai";
import { ProductionUnitContent, ProductionUnitFilter } from "./content";
import {
  ProductionUnitMaintenanceFinishModal,
  ProductionUnitMaintenanceModal,
  ProductionUnitModal,
  ProductionUnitStartFinishModal,
  ProductionUnitStartModal,
  ProductionUnitStopFinishModal,
  ProductionUnitStopModal,
} from "./modal";

export function ProductionUnitPage() {
  const [detailSideAtom, setDetailSideAtom] = useAtom(
    productionUnitDetailSideAtom
  );
  const basicTab = {
    entity: "ProductionUnitBasicDetail",
    entityTitle: "基本信息",
    api: getProductionUnit,
    infoOrList: 1,
    scheme: [
      {
        groupName: "基本信息",
        list: [
          {
            label: "名称",
            name: "name",
          },
          {
            label: "型号",
            name: "model",
          },
          {
            label: "环境",
            name: "environment",
          },
          {
            label: "功能",
            name: "function",
          },
          {
            label: "自动控制类型",
            name: "autoControlType",
          },
          {
            label: "涉及的危化品",
            name: "chemical",
          },
          {
            label: "备注",
            name: "comment",
          },
          {
            label: "是否关键生产装置",
            name: "isCritical",
            type: "enum",
            enumMap: IS_ISNOT_MAP,
          },
          {
            label: "重大危险源",
            name: "majorHazard",
            type: "entity",
          },
          {
            label: "所属危险工艺",
            name: "dangerousProcess",
            type: "entity",
          },
          {
            label: "位号",
            name: "positionNumber",
          },
          {
            label: "投用时间",
            name: "commencementTime",
            type: "date",
          },
          {
            label: "经度",
            name: "longitude",
          },
          {
            label: "纬度",
            name: "latitude",
          },
          {
            label: "负责人",
            name: "liablePerson",
            type: "entity",
          },
        ],
      },
    ],
  };
  const maintenanceTab = {
    entity: "ProductionUnitMaintenanceList",
    entityTitle: "维保信息",
    api: getProductionUnitMaintenanceList,
    params: {
      id: detailSideAtom?.id,
      values: {}, // 移除固定的分页参数，由分页功能动态管理
    },
    infoOrList: 2,
    enableFilter: true,
    filterAtom: productionUnitMaintenanceFilterAtom,
    filterColumns: [
      {
        field: "reportStatus",
        label: "上报状态",
        component: "Select",
        props: {
          placeholder: "请选择上报状态",
          optionList: JOB_REPORT_STATUS_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
    ],
    // 分页功能配置
    enablePagination: true,
    paginationConfig: {
      defaultPageSize: 10,
      pageSizeOptions: [10, 20, 50],
      showSizeChanger: true,
      showQuickJumper: true,
    },
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "检修名称", name: "name" },
            { label: "检修内容", name: "content" },
            { label: "开始时间", name: "startTime", type: "date" },
            { label: "计划结束时间", name: "planEndTime", type: "date" },
            { label: "实际结束时间", name: "endTime", type: "date" },
            { label: "备注", name: "note" },
            { label: "上报时间", name: "reportTime", type: "date" },
            {
              label: "上报状态",
              name: "reportStatus",
              type: "enum",
              enumMap: JOB_REPORT_STATUS_MAP,
            },
            { label: "上报结果", name: "reportResult" },
          ],
        },
      },
    ],
  };
  const startStopTab = {
    entity: "ProductionUnitStartStopList",
    entityTitle: "启停信息",
    api: getProductionUnitStartStopList,
    params: {
      id: detailSideAtom?.id,
      values: {}, // 移除固定的分页参数，由分页功能动态管理
    },
    infoOrList: 2,
    enableFilter: true,
    filterAtom: productionUnitStartStopFilterAtom,
    filterColumns: [
      {
        field: "startStopType",
        label: "开停车类型",
        component: "Select",
        props: {
          placeholder: "请选择开停车类型",
          optionList: PRODUCTIONUNIT_STARTSTOP_TYPE_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
      {
        field: "status",
        label: "状态",
        component: "Select",
        props: {
          placeholder: "请选择状态",
          optionList: PRODUCTIONUNIT_STARSTOP_STATUS_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
      {
        field: "reportStatus",
        label: "上报状态",
        component: "Select",
        props: {
          placeholder: "请选择上报状态",
          optionList: JOB_REPORT_STATUS_MAP.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        },
      },
    ],
    // 分页功能配置
    enablePagination: true,
    paginationConfig: {
      defaultPageSize: 10,
      pageSizeOptions: [10, 20, 50],
      showSizeChanger: true,
      showQuickJumper: true,
    },
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            {
              label: "开停车类型",
              name: "startStopType",
              type: "enum",
              enumMap: PRODUCTIONUNIT_STARTSTOP_TYPE_MAP,
            },
            {
              label: "状态",
              name: "status",
              type: "enum",
              enumMap: PRODUCTIONUNIT_STATUS_MAP,
            },
            { label: "开始时间", name: "beginTime", type: "date" },
            { label: "计划结束时间", name: "planEndTime", type: "date" },
            { label: "实际结束时间", name: "endTime", type: "date" },
            { label: "备注", name: "note" },
            { label: "上报时间", name: "reportTime", type: "date" },
            {
              label: "上报状态",
              name: "reportStatus",
              type: "enum",
              enumMap: JOB_REPORT_STATUS_MAP,
            },
            { label: "上报结果", name: "reportResult" },
          ],
        },
      },
    ],
  };
  const tabPaneList = [basicTab, maintenanceTab, startStopTab];

  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <ProductionUnitFilter />
      <ProductionUnitModal />
      <ProductionUnitMaintenanceModal />
      <ProductionUnitMaintenanceFinishModal />
      <ProductionUnitStartModal />
      <ProductionUnitStartFinishModal />
      <ProductionUnitStopModal />
      <ProductionUnitStopFinishModal />
      <ProductionUnitContent />
      <SideDetail
        entityTitle="生产装置详情"
        detailAtom={productionUnitDetailSideAtom}
        tabPaneList={tabPaneList}
      />
    </div>
  );
}
