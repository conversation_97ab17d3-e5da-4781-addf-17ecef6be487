import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useToggle } from "ahooks";
import { groupFilterAtom } from "atoms";
import { useFilterSearch } from "hooks";

export const GroupFilter = () => {
  const [state, { toggle }] = useToggle();
  const [handleSearch, handleReset] = useFilterSearch(groupFilterAtom);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="关键字搜索"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
