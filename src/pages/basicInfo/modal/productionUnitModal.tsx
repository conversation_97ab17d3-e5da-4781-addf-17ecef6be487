import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useCallback, useEffect, useMemo, useRef } from "react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getDrawAreas } from "api";
import {
  getDangerousProcessList,
  getMajorHazardList,
  productionUnitApis,
} from "api/basicInfo";
import { areaDrawerModalAtom, mapPickerAtom, productionUnitAtoms } from "atoms";
import {
  AreaMapPicker,
  EmployeeSearch,
  IS_ISNOT_MAP,
  MapPicker,
  RestSelect,
  Upload,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

interface BigScreenCustomProps {
  bigScreen?: boolean;
  id?: number;
}

export const ProductionUnitModal = (props: BigScreenCustomProps) => {
  const { bigScreen, id } = props;

  const operation = "Edit";
  const newTitle = "新增生产装置信息"; //user-defined code here
  const editTitle = "编辑生产装置信息"; //user-defined code here
  const ruleMessage = ["此为必填项!"];

  const atoms = productionUnitAtoms;
  const apis = productionUnitApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);
  const [mapPicker, setMapPicker] = useAtom(mapPickerAtom);
  const [areaDrawer, setAreaDrawer] = useAtom(areaDrawerModalAtom);
  // 如果厂家有3D模型就调用模型， 没有就调用百度地图
  const { data: areas } = useQuery({
    queryKey: ["getDrawAreas"],
    queryFn: getDrawAreas,
  });
  const cesiumUriPrefix = useMemo(() => {
    return areas?.data?.cesiumUriPrefix;
  }, [areas]);

  const unitId = editModalAtom?.id || id;

  const title = unitId ? editTitle : newTitle;
  const rules = [{ required: true, message: ruleMessage[0] }];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${unitId ?? ""}`],
    queryFn: () => {
      if (unitId) {
        return apis.get(unitId);
      }
    },
    enabled: !!unitId,
  });
  const mutation = useMutation({
    mutationFn: unitId ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data
  const { data: majorHazardList } = useQuery({
    queryKey: ["getMajorHazardList"],
    queryFn: () =>
      getMajorHazardList({
        pageNumber: 1,
        pageSize: 9999,
      }),
  });
  const majorHazardOptions = useMemo(() => {
    return majorHazardList?.data?.results ?? [];
  }, [majorHazardList]);

  const { data: dangerousProcessList } = useQuery({
    queryKey: ["getDangerouseProcessList"],
    queryFn: () =>
      getDangerousProcessList({
        pageNumber: 1,
        pageSize: 9999,
      }),
  });
  const dangerouseProcessOptions = useMemo(() => {
    return dangerousProcessList?.data?.results ?? [];
  }, [dangerousProcessList]);

  // 自动填充表单
  useEffect(() => {
    if (unitId && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      getFormApiRef.current.setValues({
        ...filterEditData(items),
        //user-defined code here
      });
    } else {
      getFormApiRef?.current?.reset?.();
    }
  }, [unitId, data, getFormApiRef]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${entity}${operation}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (unitId) {
          mutation.mutate({
            id: unitId,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  const Ele = (
    <Form
      labelPosition="left"
      labelAlign="right"
      labelWidth={100}
      getFormApi={handleSetFormApi}
    >
      {/* add form items here */}
      {(formState) => (
        <>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Input
                label="生产装置名称"
                field="name"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.Select
                label="是否关键装置"
                field="isCritical"
                className="w-full"
                rules={rules}
              >
                {IS_ISNOT_MAP.map((item) => (
                  <Form.Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Form.Select.Option>
                ))}
              </Form.Select>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Input
                label="生产装置型号"
                field="model"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <EmployeeSearch
                label="负责人"
                field="liablePersonId"
                placeholder=""
                isRequired
              />
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <RestSelect
                label="所属重大危险源"
                field="majorHazardId"
                options={majorHazardOptions}
                placeholder=""
              />
            </Col>
            <Col span={12}>
              <RestSelect
                label="所属危险工艺"
                field="dangerousProcessId"
                options={dangerouseProcessOptions}
                placeholder=""
              />
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Input
                label="装置位号"
                field="positionNumber"
                trigger="blur"
              />
            </Col>
            <Col span={12}>
              <Form.Input label="装置周围环境" field="" trigger="blur" />
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Input label="装置功能" field="environment" trigger="blur" />
            </Col>
            <Col span={12}>
              <Form.Input
                label="自动化控制方式"
                field="autoControlType"
                trigger="blur"
              />
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.DatePicker
                label="投用日期"
                field="commencementTime"
                className="w-full"
              />
            </Col>
            <Col span={12}>
              <Form.Input
                label="涉及主要危化品"
                field="chemical"
                trigger="blur"
              />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <div>
                {cesiumUriPrefix ? (
                  <AreaMapPicker data={areas} field="map" />
                ) : (
                  <MapPicker field="map" />
                )}

                <Form.Input
                  onClick={() => {
                    setMapPicker({ visible: true });
                    setAreaDrawer({
                      ...areaDrawer,
                      show: true,
                    });
                  }}
                  field="map"
                  label="定位"
                  placeholder={`请设置定位`}
                />
              </div>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
              <Upload
                label="图片"
                formField="imageList"
                field="imageList_upload"
                arrayProcessType="array"
                maxSize={2 * 51200} //KB
              />
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.TextArea label="备注" field="comment" trigger="blur" />
            </Col>
          </Row>
          {unitId ? null : <Draft id={uniqueKey} draftAtom={atoms.editModal} />}
        </>
      )}
    </Form>
  );

  if (bigScreen) {
    return Ele;
  }

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        // keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        {Ele}
      </Modal>
    </>
  );
};
